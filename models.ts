import { UserModel } from "./util/db/schemas";

export type tEnv = 'qamain' | 'qa2' | 'prod' | 'stg' | 'oci';

export type tProduct = 'checkout' | 'products'

export type tBrand = 'bluehost'
    | 'hostgator'
    | 'web'
    | 'networksolutions'
    | 'register'
    | 'domain'

export type tDrawerPage = 'pricing-bff'
    | 'upp-feature-flags'
    | 'api-tester'
    | 'kb'
    | 'storefront-uppcart'
    | 'storefront-uppdomainsearch'
    | 'domain-search'
    | 'cloudflare'
    | 'api-status';

export type tRole = 
    | "admin"
    | "cloudflare"
    | "default-user"
    | "domain-search"
    | "kb"
    | "pricing-bff"
    | "upp-feature-flags"
    | "upp"
    | "AEM"

export type DavidsDashboardRole = `NFIS-AAD-APP-DavidsDashboard-ROL-${tRole}`;

export interface iUserStore {
    user?: MsOauthResponseModel,
    setUser: (val: iUserStore['user']) => void;

    roles: DavidsDashboardRole[],
    setRoles: (val: iUserStore['roles']) => void
};

export interface CommonResponseModel<T> {
    success: boolean;
    errorMessage?: string;
    data?: T
}

export interface LoginSuccessModel {

}

export interface LoginFailureModel {

}

export type DeployStatus = Record<tEnv, string>;

export interface RowData {
    id: number;
    branch: string;
    brand: string;
    env: tEnv;
    build: number | string;
    newDeploy?: boolean;
    build_file: string;
    install_date: string;
}
export enum ServiceStatus {
    ONLINE = "ONLINE",
    UNRECHEABLE = "UNRECHEABLE"
}

export interface iServiceResponseStatus {
    URI: string;
    version: string;
    status: ServiceStatus;
}

export type tDeployStatus = Record<tEnv, string>

export interface MsOauthResponseModel {
    email: string;
    name: string;
    sub: string;
    iat: number;
    exp: number;
    jti: string;
    roles: string[];
}

export interface QuickLinkModel {
    name: string;
    url: string;
    icon: JSX.Element;
}


export type AdserverBrandsModel = 'BLUEHOST' | 'WEB' | 'NETWORKSOLUTIONS' | 'HOSTGATOR';
export type AdserverContainerModel = 'Upp' | 'Amhp' | 'Amwi';