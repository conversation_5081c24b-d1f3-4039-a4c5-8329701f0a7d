import { AdserverBrandsModel, AdserverContainerModel, DavidsDashboardRole, QuickLinkModel, tBrand, tD<PERSON>erPage, tRole } from "./models";
import MicrosoftEntraProvider from "next-auth/providers/azure-ad";
import jwt from 'jsonwebtoken';
import { NextAuthOptions } from 'next-auth'
import {
    Flag, CloudLightning,
    ShoppingCart, SearchSlash, DollarSign,
    SearchCheckIcon, Webhook, BookA, SquareActivity
} from 'lucide-react';

import { CloudflareIcon, CsrToolsIcon, DatadogIcon, DocumentationIcon, JenkinsIcon, AdsIcon } from "./components/icons";


export const QUICK_LINKS: QuickLinkModel[] = [
    { 
        name: "UPPCart Datadog", 
        url: "https://app.datadoghq.com/dashboard/dkv-d2z-y49/storefront-upp-cart-logger?fromUser=false&refresh_mode=sliding&from_ts=1746189960776&to_ts=1746794760776&live=true" ,
        icon: DatadogIcon
    },
    {
        name: "Products Datadog",
        url: "https://app.datadoghq.com/dashboard/zad-avz-9cm/storefront-uppdomainsearch-logger?fromUser=false&refresh_mode=sliding&from_ts=1746783364209&to_ts=1746797764209&live=true",
        icon: DatadogIcon
    },
    {
        name: "AEM Pricing",
        url: "https://app.datadoghq.com/dashboard/88m-p6u-etb/aem-pricing?fromUser=false&refresh_mode=sliding&from_ts=1746193106854&to_ts=1746797906854&live=true",
        icon: DatadogIcon
    },
    {
        name: "Content BFF",
        url: "https://app.datadoghq.com/dashboard/9cj-2dm-ja5/content-bff?fromUser=false&refresh_mode=sliding&from_ts=1746711516775&to_ts=1746797916775&live=true",
        icon: DatadogIcon
    },
    {
        name: "Jarvis Ms Domain Search",
        url: "https://app.datadoghq.com/dashboard/g4v-hh5-b3v/jarvis-ms-domain-search?fromUser=false&refresh_mode=sliding&from_ts=1746625126788&to_ts=1746797926788&live=true",
        icon: DatadogIcon
    },
    {
        name: "NFD Ad Server",
        url: "https://app.datadoghq.com/dashboard/cs2-pu9-kzb/nfd-ad-server?fromUser=false&refresh_mode=sliding&from_ts=1746711535269&to_ts=1746797935269&live=true",
        icon: DatadogIcon
    },
    {
        name: "UPP Prod",
        url: "https://jenkins-prod.newfold.com/job/Storefront/job/checkout_multibranch_prod/",
        icon: JenkinsIcon
    },
    {
        name: "UPP QA",
        url: "https://jenkins-prod.newfold.com/job/Storefront/job/checkout_multibranch_qa/",
        icon: JenkinsIcon
    },
    {
        name: "UPP Deploy",
        url: "https://jenkins.web.web.com/view/Storefront/job/Storefront/job/checkout_multibranch_deploy_prod/",
        icon: JenkinsIcon
    },
    {
        name: "CSR Tools",
        url: "https://csrtools.prod.netsol.com/",
        icon: CsrToolsIcon
    },
    {
        name: "Yggdrasil docs",
        url: "https://yggdrasil-docs-storefrontbff.apps.atlanta1.newfoldmb.com/",
        icon: DocumentationIcon
    },
    {
        name: "Cloudflare",
        url: "https://dash.cloudflare.com/6457c695f26fb41cb45badbd46352565/newfold.com",
        icon: CloudflareIcon
    }
];

export const ROLES: tRole[] = [
    "admin",
    "cloudflare",
    "default-user",
    "domain-search",
    "kb",
    "pricing-bff",
    "upp-feature-flags",
    "upp",
    "AEM",
];

export const DRAWER_ITEMS: Record<tDrawerPage, JSX.Element> = {
    'upp-feature-flags': <Flag />,
    'cloudflare': <CloudLightning />,
    'storefront-uppcart': <ShoppingCart />,
    'storefront-uppdomainsearch': <SearchSlash />,
    'pricing-bff': <DollarSign />,
    'domain-search': <SearchCheckIcon />,
    'api-tester': <Webhook />,
    'kb': <BookA />,
    'api-status': <SquareActivity />,
};

export const OPEN_DRAWER_ITEMS: Record<string, JSX.Element> = {
    'adserver-renderapp': AdsIcon
}

export const SESSION_KEY = "davids-tools-session-token";

export const KB_BRANDS = [
    'aso',
    'arvixe',
    'bluehost',
    'crazydomains',
    'freeparking',
    'hostgator',
    'netfirms',
    'netsol',
    'register',
    'tester',
    'web',
    'vodien'
];

export const KB_CLEAR_CACHE_TOOLTIP = `Every brand's Knowledgebase caches the ads and AEM content for faster response times. However, this also means that if you make a change to the cached content upstream, you will not immediately see the changes reflected. Therefore, use this button to forcibly clear the selected brand's cache`;

export const STATUS_ENDPOINTS = [
    "https://sfbff.newfold.com/getContent/status",
    "https://sfbff.newfold.com/logEvents/status",
    "https://sfbff.newfold.com/locales/status",
    "https://sfbff.newfold.com/getProductPrice/status",
];

export const BRANDS: tBrand[] = [
    'bluehost',
    'hostgator',
    'web',
    'networksolutions',
    'register',
    'domain'
];

export const authOptions: NextAuthOptions = {
    providers: [
        MicrosoftEntraProvider({
            clientId: process.env.AZURE_AD_CLIENT_ID!,
            clientSecret: process.env.AZURE_AD_CLIENT_SECRET!,
            tenantId: process.env.AZURE_AD_TENANT_ID!,
            profile: profile => ({
                id: profile.sub,
                all: profile,
                name: profile.name,
                email: profile.name,
                groups: profile.groups
            }),
            authorization: {
                params: {
                    scope: "openid profile email",
                },
            },
        }),
    ],
    secret: process.env.NEXTAUTH_SECRET!,
    session: { strategy: "jwt" as any },
    callbacks: {
        async signIn({ account }) {
            const decoded: any = jwt.decode(account!.id_token!);
            if (decoded.roles) {
                (account as any).roles = decoded.roles;
            }

            console.log(decoded);
            return true;
        },
        async jwt({ token, account }) {
            if (account) {
                const decoded: any = jwt.decode(account.id_token!);
                token.roles = decoded.groups || []; // Store roles in token
                // console.log('test', decoded);
            }
            // console.log('JWT Token:', token); // Debugging
            return token;
        },
        session: x => {
            // console.log('session', x);
            return x.token as any;
        }
    }
};

export const AUTH_ROLES: Record<string, DavidsDashboardRole> = {
    "251cc2f8-1746-411d-8603-d587ab946bac": "NFIS-AAD-APP-DavidsDashboard-ROL-admin",
    "8a944c2f-7121-4fe1-b3bb-b943af71d004": "NFIS-AAD-APP-DavidsDashboard-ROL-cloudflare",
    "5c9c4595-825c-4a49-85e5-a0b86c29910a": "NFIS-AAD-APP-DavidsDashboard-ROL-default-user",
    "1e224376-3b36-4202-9ab2-b41884259474": "NFIS-AAD-APP-DavidsDashboard-ROL-domain-search",
    "8dff42cd-839a-4edb-8b69-16c9dc14c55a": "NFIS-AAD-APP-DavidsDashboard-ROL-kb",
    "96363f58-0773-41a0-be46-e0fa53bee708": "NFIS-AAD-APP-DavidsDashboard-ROL-pricing-bff",
    "e255f009-530c-40d5-abee-54f897ac31e5": "NFIS-AAD-APP-DavidsDashboard-ROL-upp-feature-flags",
    "6151d3d8-26cf-4e61-b626-253c654cc4a4": "NFIS-AAD-APP-DavidsDashboard-ROL-upp",
    "c7abd689-a775-404c-a6ba-ade41e9bc9d7": "NFIS-AAD-APP-DavidsDashboard-ROL-AEM"
}
export const BFF_PRODUCT_SKUS: Record<string, string> = {
    "4341": "Economy",
    "4342": "Premium",
    "4343": "Ultimate",
    "527": "Economy",
    "528": "Premium",
    "529": "Ultimate",
    "4477": "Professional",
    "4478": "Business",
    "4140": "Standard",
    "4141": "Premium",
    "4142": "Ultimate",
    "1846": "15 Days Trial",
    "4635": "Website Only",
    "4637": "Website + Marketing",
    "4639": "Website + eCommerce",
    "2600": "Design",
    "2604": "eCommerce",
    "3031": "Design (Hosting)",
    "3033": "eCommerce (Hosting)",
    "2606": "WordPress Business Design",
    "2608": "WordPress eCommerce Design",
    "4751": "Website Maintenance",
    "2644": "Bronze",
    "2645": "Silver",
    "2646": "Gold",
    "4469": "Silver (monthly)",
    "4471": "Gold (monthly)",
    "3624": "Group Mail",
    "3763": "Promo Mail",
    "3765": "The Promoter",
    "565": "Online Startup",
    "566": "Business Builder",
    "567": "Ultimate Empire",
    "4333": "Grow Enterprise",
    "4780": "Domain Guard",
    "3591": "Logo Plus Subscription",
    "3593": "Logo Design - 4 Concepts",
    "884": "Basic Simple SEO",
    "885": "Premium Simple SEO",
    "886": "Ultimate Simple SEO",
    "4251": "Online Store",
    "4261": "Online Store + Marketplace",
    "178": "Economy Email Exchange",
    "179": "Premium Email Exchange",
    "180": "Business Email Exchange",
    "825": "Business Directory",
    "788": "Windows Hosting - Economy",
    "789": "Windows Hosting - Premium",
    "790": "Windows Hosting - Unlimited",
    "4163": "Business Starter",
    "4165": "Business Standard",
    "4167": "Business Plus",
    "4169": "Enterprise Standard",
    "4171": "Enterprise Plus",
    "17": "Traffic Booster",
    "4143": "Premium Email Protection - FREE Trial",
    "116": "Premium Email Protection",
    "4320": "Dedicated Server - Enterprise Small",
    "4323": "Dedicated Server - Enterprise+ Small",
    "4326": "Dedicated Server - Storage Small",
    "4321": "Dedicated Server - Enterprise Medium",
    "4322": "Dedicated Server - Enterprise Large",
    "4324": "Dedicated Server - Enterprise+ Medium",
    "4325": "Dedicated Server - Enterprise+ Large",
    "4327": "Dedicated Server - Storage Medium",
    "4328": "Dedicated Server - Storage Large",
    "74": "Standard SSL",
    "499": "Premium SSL EV",
    "2594": "DV Wildcard SSL",
    "744": "Standard DNS",
    "63": "DNS Service",
    "544": "Site Protection",
    "5": "My Fax 500 Receives",
    "6": "Fax Plus 1,500 Receives",
    "7": "Fax Pro 3,000 Receives",
    "817": "Cloud Backup 1 GB",
    "818": "Cloud Backup 5 GB",
    "819": "Cloud Backup 25 GB",
    "820": "Cloud Backup 100 GB",
    "821": "Cloud Backup 500 GB",
    "828": "Cloud Backup 1 TB",
    "829": "Cloud Backup 2 TB",
    "830": "Cloud Backup 5 TB",
    "831": "Cloud Backup 10 TB",
    "842": "Site Backup 1 GB",
    "843": "Site Backup 5 GB",
    "844": "Site Backup 10 GB",
    "845": "Site Backup 30 GB",
    "847": "Site Backup 100 GB",
    "596": "Standard Linux Server",
    "597": "Professional Linux Server",
    "598": "Enterprise Linux Server",
    "599": "Standard Windows Server",
    "600": "Professional Windows Server",
    "601": "Enterprise Windows Server",
    "168": "Web Analytics",
    "925": "Domain Listing - Starter",
    "927": "Domain Listing - Professional",
    "929": "Domain Listing - Enterprise",
    "1234": "webValue",
    "1477": "webPlus",
    "1478": "webExpert",
    "4131": "Economy",
    "3132": "Premium",
    "3133": "Unlimited",
    "1242": "bizValue",
    "1535": "bizPlus",
    "1537": "bizElite",
    "4449": "Titan Email - Personal Plan - 1 Account",
    "4450": "Titan Email - Professional Plan - 1 Account",
    "4451": "Titan Email - Business Plan - 1 Account",
    "1975": "wpValue",
    "1976": "wpPlus",
    "1977": "wpUltimate",
    "4800": "NVMe 16",
    "4801": "NVMe 24",
    "4802": "NVMe 32",
    "4803": "NVMe 64",
    "4804": "NVMe 128",
    "2172": "Standard SSL",
    "2173": "Premium SSL + EV",
    "2593": "DV Wildcard SSL"
}

export const ADSERVER_BRAND_MAPPER: Record<AdserverBrandsModel, string> = {
  BLUEHOST: "bh",
  HOSTGATOR: "hg",
  NETWORKSOLUTIONS: "netsol",
  WEB: "wcom",
};

export const ADSERVER_CONTAINER_MAPPER: Record<AdserverContainerModel, string> = {
  Amhp: "amhp",
  Amwi: "amwi",
  Upp: "uppcart",
};