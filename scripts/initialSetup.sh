if ! command -v jq &> /dev/null
then
    echo "jq could not be found"
    exit 1
fi
app_name=$(cat package.json | jq '.org' | sed 's/"//g')

echo $app_name

oc new-build -n davids-tools --strategy=docker --name=$app_name --binary
oc start-build $app_name -n davids-tools --from-dir=. --follow
oc new-app $app_name -n davids-tools
oc expose svc/$app_name -n davids-tools
oc expose deployment/$app_name -n davids-tools --port=8080 --target-port=8080 --name=$app_name
oc get pods