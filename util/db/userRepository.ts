import { db } from ".";
import { UserModel, usersTable } from "./schemas";
import { eq, and } from "drizzle-orm";

export async function createUser(user: UserModel) {
    try {

        const createdUser = await db.insert(usersTable).values(user).returning();
        console.log('new user created');

        return createdUser;
    }
    catch (e) {
        console.error('error creating user');
        console.error(e);
    }
}

export async function createUserIfNotExist(user: UserModel) {
    try {
        const existingUser = await getUser(user.email);

        if(!existingUser) {
            await db.insert(usersTable).values(user);
            console.log('new user created');
        }
    }
    catch (e) {
        console.error('error creating user');
        console.error(e);
    }
}

export async function getUser(email: string) {
    let retVal: typeof usersTable.$inferInsert | undefined;
    try {
        const user = await db.select().from(usersTable).where(eq(usersTable.email, email));
        retVal = user[0];
    }
    catch (e) {
        console.error('error searching for user');
        console.error(e);

        retVal = undefined;
    }
    finally {
        return retVal;
    }
}

export async function checkIfUserExists(email: string, password: string) {
    let retVal: typeof usersTable.$inferInsert | undefined;
    try {
        const user = await db.select().from(usersTable).where(
            and(
                eq(usersTable.email, email),
                eq(usersTable.password, password)
            )
        );
        retVal = user[0];
    }
    catch (e) {
        console.error('error searching for user');
        console.error(e);

        retVal = undefined;
    }
    finally {
        return retVal;
    }
}