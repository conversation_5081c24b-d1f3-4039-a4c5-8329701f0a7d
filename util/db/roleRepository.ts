import { db } from ".";
import { rolesTable, usersTable } from "./schemas";
import { eq } from "drizzle-orm";

export async function createRole(role: typeof rolesTable.$inferInsert) {
    try {
        await db.insert(rolesTable).values(role);
        console.log(`successfully created role ${role.name}`);
    }
    catch(e) {
        console.error(`unable to create role ${role.name}`);
        console.error(e);
    }
}