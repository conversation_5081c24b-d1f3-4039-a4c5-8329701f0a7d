import { sql } from 'drizzle-orm';
import { integer, pgTable, varchar, text, timestamp } from 'drizzle-orm/pg-core';

import type { InferInsertModel } from 'drizzle-orm';

export const usersTable = pgTable("users", {
    id: integer().primaryKey().generatedByDefaultAsIdentity(),
    email: varchar({ length: 100 }).notNull(),
    password: varchar({ length: 100 }).notNull(),
    roles: text('roles').array().notNull().default(sql`ARRAY[]::text[]`)
});

export const rolesTable = pgTable("roles", {
    id: integer().primaryKey().generatedByDefaultAsIdentity(),
    name: varchar({ length: 100 }).notNull()
});

export const sessionTable = pgTable("sessions", {
    id: text("id").primaryKey(),
	userId: integer("user_id")
		.notNull()
		.references(() => usersTable.id),
	expiresAt: timestamp("expires_at", {
		withTimezone: true,
		mode: "date"
	}).notNull()
});

export const skuTable = pgTable("skus", {
    id: integer().primaryKey().generatedByDefaultAsIdentity(),
    sku: varchar({ length: 100 }).primaryKey().notNull()
})

export const adsTable = pgTable("ads", {
    id: integer().primaryKey().generatedByDefaultAsIdentity(),
    name: varchar({ length: 100 }).notNull(),
    brand:  varchar({ length: 100 }).notNull(),
    containerName:  varchar({ length: 100 }).notNull(),
    actionType:  varchar({ length: 100 }).notNull(),
    // url:  varchar({ length: 100 }).notNull(),
    productsInCart: text('productsInCart').array().notNull().default(sql`ARRAY[]::text[]`),
    priority: integer().notNull(),
    
})

export type UserModel = InferInsertModel<typeof usersTable>;
export type RoleModel = InferInsertModel<typeof rolesTable>;
export type SessionModel = InferInsertModel<typeof sessionTable>;