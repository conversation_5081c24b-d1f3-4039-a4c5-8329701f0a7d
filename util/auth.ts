import NextAuth, { Session } from "next-auth";
import { AUTH_ROLES, authOptions } from "@/constants";
import { Davids<PERSON><PERSON>boardRole, MsOauthResponseModel, tRole } from "@/models";

export const authHandler = NextAuth(authOptions);

export function userHasRole(user: MsOauthResponseModel, role: tRole): boolean {
    return Boolean(
        user.roles.find(r  => r === role)
    );
}

export function roleToDavidsDashboardRole(role: tRole) {
    return `NFIS-AAD-APP-DavidsDashboard-ROL-${role}`;
}

export function transformSession(session: Session): MsOauthResponseModel {
    return session as unknown as MsOauthResponseModel
}

export function davidsDashboardRoleToTRole(role: DavidsDashboardRole) {
    return role.split('-')[5];
}