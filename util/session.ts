import { type UserModel, type SessionModel, sessionTable, usersTable } from './db/schemas';
import { encodeBase32LowerCaseNoPadding, encodeHexLowerCase } from "@oslojs/encoding";
import { sha256 } from '@oslojs/crypto/sha2'
import { db } from './db';
import { eq } from 'drizzle-orm';

export type SessionValidationResult =
    | { session: SessionModel; user: UserModel }
    | { session: null; user: null };


export function generateSessionToken(): string {
    const bytes = new Uint8Array(20);
    crypto.getRandomValues(bytes);

    const token = encodeBase32LowerCaseNoPadding(bytes);
    return token;
}

export async function createSession(token: string, userId: number): Promise<SessionModel> {
    const sessionId = encodeHexLowerCase(sha256(new TextEncoder().encode(token)));
    const session: SessionModel = {
        id: sessionId,
        userId,
        expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30)
    };

    await db.insert(sessionTable).values(session);
    return session;
}

export async function validateSessionToken(token: string): Promise<SessionValidationResult> {
    // const sessionId = encodeHexLowerCase(new TextEncoder().encode(token)); 
    const result = await db
        .select({ users: usersTable, session: sessionTable })
        .from(sessionTable)
        .innerJoin(usersTable, eq(sessionTable.userId, usersTable.id))
        .where(eq(sessionTable.id, token));

    if (result.length < 1) {
        return { session: null, user: null }
    }

    const { session, users } = result[0];

    if (Date.now() >= session.expiresAt.getTime()) {
        await db.delete(sessionTable).where(eq(sessionTable.id, session.id));
        return { session: null, user: null }
    }

    if (Date.now() >= session.expiresAt.getTime() - 1000 * 60 * 60 * 24 * 15) {
        session.expiresAt = new Date(Date.now() + 1000 * 60 * 60 * 24 * 30);
        await db
            .update(sessionTable)
            .set({
                expiresAt: session.expiresAt
            })
            .where(eq(sessionTable.id, session.id));
    }

    return { session, user: users }

}

export async function invalidateSession(sessionId: string): Promise<void> {
    await db.delete(sessionTable).where(eq(sessionTable.id, sessionId));
}

export async function deleteAllSessionsForUser(user: UserModel) {
    const result = await db
        .delete(sessionTable).where(eq(sessionTable.userId, user.id!));
}