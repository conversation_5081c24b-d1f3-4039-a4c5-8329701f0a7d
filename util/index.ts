import { tBrand, tDeployStatus, tEnv, tProduct } from "@/models";
import axios from "axios";

export function reduceArrayToObj(deployData: any): tDeployStatus {
    const obj: tDeployStatus = deployData.reduce((acc: any, { env, data }: any) => {
        (acc as any)[env] = { dataString: data, env };
        return acc;
    }, {} as tDeployStatus);

    return obj;
}

export async function getDeployStatusBulk(brands: tBrand[], env: tEnv, stub: tProduct): Promise<tDeployStatus> {
    return reduceArrayToObj(await Promise.all(
        brands.map(async brand => {
            return {
                env: brand,
                data: (await axios.get(`${getHost()}/upp/build-info?env=${env}&brand=${brand}&path=${stub}`)).data
            }
        })
    )) as tDeployStatus;
}

export function extractStringToObj(input: any, env: tEnv): any {
    const result: Record<string, string | number | string[]> = {};

    input.dataString.split("\n").forEach((line: any) => {
        const [key, ...valueParts] = line.split(":");

        if (valueParts.length > 0) {
            const value = valueParts.join(":").trim();
            const formattedKey = key.toLowerCase().replace(/\s+/g, "_");
            result[formattedKey] = isNaN(Number(value)) ? value : Number(value);
        }
        result.env = env;
        result.brand = input.env
    });
    return result;
}

const DEV = false;
export function getHost() {
    if(DEV) return 'http://localhost:3001';
    else return 'https://new-proxy-davids-tools.apps.atlanta1.newfoldmb.com';
}
