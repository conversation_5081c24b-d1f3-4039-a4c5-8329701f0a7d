'use client';

import React from "react";
import Countdown from 'react-countdown';

import PageLayout from "@/components/pageLayout";
import DeploymentStatusTable from "@/components/sf/deploymentStatusTable";
import { Skeleton } from "@/components/ui/skeleton";
import { extractStringToObj, getDeployStatusBulk, getHost } from "@/util";
import { BRANDS } from "@/constants";
import { Button } from "@/components/ui/button";

export default function Storefront() {
    const [rowsQaMain, setRowsQaMain] = React.useState<any[]>([]);
    const [rowsProd, setRowsProd] = React.useState<any[]>([]);
    const [rowsQa2, setRowsQa2] = React.useState<any[]>([]);
    const [rowsStg, setRowsStg] = React.useState<any[]>([]);
    const [rowsOci, setRowsOci] = React.useState<any[]>([]);
    const [countdown, setCountdown] = React.useState<number>(0);

    const loadData = React.useCallback(async () => {

        const [prodObj, qaMainObj, qa2Obj, stgObj, ociObj] = await Promise.all([
            getDeployStatusBulk(BRANDS, 'prod', 'checkout'),
            getDeployStatusBulk(BRANDS, 'qamain', 'checkout'),
            getDeployStatusBulk(BRANDS, 'qa2', 'checkout'),
            getDeployStatusBulk(BRANDS, 'stg', 'checkout'),
            getDeployStatusBulk(BRANDS, 'oci', 'checkout'),
        ]);
        setRowsQaMain(Object.keys(qaMainObj).map(key => extractStringToObj((qaMainObj as any)[key], 'qamain')));
        setRowsProd(Object.keys(prodObj).map(key => extractStringToObj((prodObj as any)[key], 'prod')));
        setRowsQa2(Object.keys(qa2Obj).map(key => extractStringToObj((qa2Obj as any)[key], 'qamain')));
        setRowsStg(Object.keys(stgObj).map(key => extractStringToObj((stgObj as any)[key], 'stg')));
        setRowsOci(Object.keys(ociObj).map(key => extractStringToObj((ociObj as any)[key], 'oci')));

    }, [setRowsQaMain, setRowsProd, setRowsQa2, setRowsStg]);

    function refreshData() {
        setRowsQaMain([]);
        setRowsProd([]);
        setRowsQa2([]);
        setRowsStg([]);
        setRowsOci([]);

        loadData();
    }

    async function flushCache() {
        setCountdown(60);
        await restartServer();
    }

    async function restartServer() {
        await fetch(`${getHost()}/refresh`);
    }

    React.useEffect(() => {
        loadData();
    }, [loadData]);

    return (<PageLayout>
        <div className="w-full flex flex-col gap-4">
            <div className="flex flex-row items-center gap-4">
                <h2 className="text-base-content text-2xl ">UPPCart Deployment Status</h2>
                <Button className="bg-transparent glass-card" onClick={refreshData} variant={'outline'}>Refresh</Button>
                <Button className="bg-transparent glass-card" onClick={flushCache} variant={'outline'}>Flush Dashboard Cache (will take 1 min)</Button>
                {countdown > 0 && <Countdown onComplete={() => {setCountdown(0); loadData();}} date={Date.now() + 1000 * countdown} />}
            </div>
            <div className="flex flex-col gap-4">
                {rowsProd?.length > 0 ? <DeploymentStatusTable rows={rowsProd} title="PROD" /> : <Skeleton />}
                {rowsQaMain?.length > 0 ? <DeploymentStatusTable rows={rowsQaMain} title="QAMain" /> : <Skeleton />}
                {rowsQa2?.length > 0 ? <DeploymentStatusTable rows={rowsQa2} title="QA2" /> : <Skeleton />}
                {rowsStg?.length > 0 ? <DeploymentStatusTable rows={rowsStg} title="STG" /> : <Skeleton />}
            </div>
                {rowsOci?.length > 0 ? <DeploymentStatusTable rows={rowsOci} title="OCI" /> : <Skeleton />}
        </div>
    </PageLayout >)
}