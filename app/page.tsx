'use client';

import React from "react";
import PageLayout from "@/components/pageLayout";
import { DRAWER_ITEMS, QUICK_LINKS } from "@/constants";
import Link from "next/link";
import { signIn, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";

function UserNotLoggedIn() {
  return (<div className="h-40 flex flex-col items-center justify-center">
    <button className="btn text-xl glass-card p-2" onClick={() => signIn("microsoft")}>pls login</button>
  </div>);
}

export default function Home(params: any) {
  const { data } = useSession();
  const router = useRouter();

  React.useEffect(() => {
    if (params.searchParams.callbackUrl) {
      router.push(params.searchParams.callbackUrl)
    }
  }, [router, params.searchParams.callbackUrl]);

  return (
    <PageLayout>
      {Boolean(data) ? <>
        <p className="text-xl py-4">Navigation</p>
        <div className="grid grid-cols-3 gap-4 glass-card py-10">
          {Object.keys(DRAWER_ITEMS).map((key, index) => (
            <button key={index} className="btn-secondary btn-outline btn h-16">
              <Link href={`/${key}`} className="text-center text-secondary-content p-4" key={index}>
                <Button className="w-96 h-16 bg-transparent text-white border-white" variant={'outline'}>
                  {(DRAWER_ITEMS as any)[key]}
                  <p>{key}</p>
                </Button>
              </Link>
            </button>
          ))}
        </div></> : <UserNotLoggedIn />}

      <div className="my-10"></div>
      <p className="my-4">Quick Links</p>
      <div className="grid grid-cols-10 md:grid-cols-5 xl:grid-cols-10 gap-4">
        {QUICK_LINKS.map((i, index) => (<Link key={index} rel="noopener noreferrer" target="_blank" href={i.url} className="w-32 glass-card flex flex-col items-center">
          <div className="pt-2">{i.icon}</div>
          <p className="text-center">{i.name}</p>
        </Link>))}
      </div>
    </PageLayout>
  );
}
