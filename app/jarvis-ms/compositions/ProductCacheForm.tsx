import React, { useState, useEffect } from 'react';
import { TextField, Button, Box, MenuItem } from '@mui/material';
import { clearProductCache } from '@/actions/jarvisMsAction';
import { BRANDS, CURRENCIES, TRANSACTION_TYPES } from '@/constants';
import ResponseModal from './ResponseModal';

const ProductCacheForm = ({ env: string }) => {
  const initialForm = {
    brand: '',
    productCode: '',
    currencyCode: '',
    transactionType: '',
    siteId: '',
    couponCode: '',
  };

  const [form, setForm] = useState(initialForm);
  const [availableCurrencies, setAvailableCurrencies] = useState([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMessage, setModalMessage] = useState('');

  useEffect(() => {
    if (form.brand) {
      setAvailableCurrencies(CURRENCIES[form.brand] || []);
      setForm((prev) => ({ ...prev, currencyCode: '' }));
    }
  }, [form.brand]);

  const handleChange = (e) => setForm({ ...form, [e.target.name]: e.target.value });

  const handleSubmit = async () => {
    const { brand, productCode, currencyCode, transactionType } = form;
    if (!env || !brand || !productCode || !currencyCode || !transactionType) {
      setModalMessage('Please fill all mandatory fields.');
      setModalOpen(true);
      return;
    }

    const payload = {
      productCode,
      currencyCode,
      transactionType,
      siteId: form.siteId || undefined,
      couponCode: form.couponCode || undefined,
    };

    try {
      const result = await clearProductCache(env, brand, payload);
      setModalMessage(result.message);
      if (result.success) {
        setForm(initialForm); // Reset form only on success
      }
    } catch (err) {
      setModalMessage(`❌ Error: ${err.message}`);
    } finally {
      setModalOpen(true);
    }
  };

  return (
    <Box>
      <TextField
        select
        label="Brand"
        name="brand"
        value={form.brand}
        onChange={handleChange}
        fullWidth
        required
        margin="normal"
      >
        {BRANDS.map((b) => (
          <MenuItem key={b} value={b.toUpperCase()}>{b}</MenuItem>
        ))}
      </TextField>

      <TextField label="Product Code" name="productCode" value={form.productCode} onChange={handleChange} fullWidth required margin="normal" />

      <TextField
        select
        label="Currency Code"
        name="currencyCode"
        value={form.currencyCode}
        onChange={handleChange}
        fullWidth
        required
        margin="normal"
        disabled={!form.brand}
      >
        {availableCurrencies.map((cur) => (
          <MenuItem key={cur} value={cur}>{cur}</MenuItem>
        ))}
      </TextField>

      <TextField
        select
        label="Transaction Type"
        name="transactionType"
        value={form.transactionType}
        onChange={handleChange}
        fullWidth
        required
        margin="normal"
      >
        {TRANSACTION_TYPES.map((type) => (
          <MenuItem key={type} value={type}>{type}</MenuItem>
        ))}
      </TextField>

      <TextField label="Site ID" name="siteId" value={form.siteId} onChange={handleChange} fullWidth margin="normal" />
      <TextField label="Coupon Code" name="couponCode" value={form.couponCode} onChange={handleChange} fullWidth margin="normal" />

      <Button variant="contained" onClick={handleSubmit}>Clear Product Cache</Button>

      <ResponseModal open={modalOpen} onClose={() => setModalOpen(false)} message={modalMessage} />
    </Box>
  );
};

export default ProductCacheForm;