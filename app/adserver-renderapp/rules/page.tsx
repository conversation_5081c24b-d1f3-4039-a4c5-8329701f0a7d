import PageLayout from "@/components/pageLayout";
import Rule<PERSON>ontainer from "./RuleContainer";
import { AdserverBrandsModel, AdserverContainerModel } from "@/models";
import { ADSERVER_BRAND_MAPPER, ADSERVER_CONTAINER_MAPPER } from "@/constants";





export default async function AdserverRenderappRules() {
  const promises = Object.keys(ADSERVER_BRAND_MAPPER).flatMap((brand) => {
    const brandValue = ADSERVER_BRAND_MAPPER[brand as AdserverBrandsModel];

    return Object.keys(ADSERVER_CONTAINER_MAPPER).map((container) => {
      const containerValue = ADSERVER_CONTAINER_MAPPER[container as AdserverContainerModel];
      return (async () => {
        const res = await fetch(
          `https://sfbff.newfold.com/getContent/json/assets/adserver-ads/${containerValue}/${brandValue}-${containerValue}-ads.json`,
          {
            // Ensures static generation
            next: { revalidate: 60 * 2 }, // revalidate every 2 mins (ISR)
          }
        );
        const json = await res.json();
        const rules = JSON.parse(json.attributes).rules;
        return {
          key: `${brandValue}-${containerValue}`,
          value: rules,
        };
      })();
    });
  });

  const results = await Promise.all(promises);
  const data = Object.fromEntries(results.map(({ key, value }) => [key, value]));

  return (
    <PageLayout>
      <RuleContainer data={data} />
    </PageLayout>
  );
}
