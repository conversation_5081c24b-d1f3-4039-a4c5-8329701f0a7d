import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardContent,
  RuleAttr,
} from "@/components/ui/card";

type FragmentDetails = {
  adSku: string;
  sku: string;
  adType: string;
  htmlPath?: string;
};

type Rule = {
  actionType: string;
  alias: string;
  brand: string;
  businessRule: string;
  channelID: string;
  containerName: string;
  coupon: string;
  eligibility: string;
  fragmentDetails: FragmentDetails;
  fragmentJsonPath: any[];
  landingCode: string;
  name: string;
  priceDisplayTerm: string;
  pricingTerm: string;
  priority: number;
  url: string;
  weightage: number;
};

interface Props {
  rule: Rule;
}

const AdRuleCard: React.FC<Props> = ({ rule }) => {
  return (
    <Card className="w-full ">
      <CardHeader>
        <CardTitle>{rule.name ?? rule.alias}</CardTitle>
        <CardDescription>{rule.eligibility}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-1 text-sm text-muted-foreground">
        <table className="w-full text-left border-collapse">
          <tbody>
            <RuleAttr label="Alias" value={rule.alias} />
            <RuleAttr label="Weightage" value={rule.weightage} />
            <RuleAttr label="Landing Code" value={rule.landingCode} />
            <RuleAttr label="Container" value={rule.containerName} />
            <RuleAttr label="Business Rule" value={rule.businessRule} />
            <RuleAttr label="URL" value={rule.url} />
            <RuleAttr
              label="Pricing Term"
              value={`${rule.pricingTerm} (${rule.priceDisplayTerm})`}
            />
            <RuleAttr label="Priority" value={rule.priority} />
            <RuleAttr
              label="Fragment"
              value={`${rule.fragmentDetails.adSku} / ${rule.fragmentDetails.adType}`}
            />
          </tbody>
        </table>
      </CardContent>
    </Card>
  );
};

export default AdRuleCard;
