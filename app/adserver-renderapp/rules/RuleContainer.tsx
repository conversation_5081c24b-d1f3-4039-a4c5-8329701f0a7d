"use client";
import { AdserverBrandsModel, AdserverContainerModel } from "@/models";
import { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ADSERVER_BRAND_MAPPER, ADSERVER_CONTAINER_MAPPER } from "@/constants";
import AdRuleCard from "./AdRuleCard";

interface Props {
  data: Record<string, any[]>;
}

export default function RuleContainer({ data }: Props) {
  const [selectedBrand, setSelectedBrand] =
    useState<AdserverBrandsModel>("BLUEHOST");
  const [selectedContainer, setSelectedContainer] =
    useState<AdserverContainerModel>("Amhp");

  const brandPrefix = ADSERVER_BRAND_MAPPER[selectedBrand];
  const containerKey = ADSERVER_CONTAINER_MAPPER[selectedContainer];
  const key = `${brandPrefix}-${containerKey}`;
  const rules = data[key] || [];

  return (
    <div className="flex flex-col">
      <div className="flex flex-row gap-6 glass-card p-2">
        <Select
          defaultValue="BLUEHOST"
          onValueChange={(e) => {
            setSelectedBrand(e as AdserverBrandsModel);
          }}
        >
          <SelectTrigger className="w-[300px]">
            <SelectValue placeholder="Brand" />
          </SelectTrigger>
          <SelectContent>
            {Object.keys(ADSERVER_BRAND_MAPPER).map((item, index) => {
              return (
                <SelectItem key={index} value={item}>
                  {item}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>

        <Select
          defaultValue="Amhp"
          onValueChange={(e) => {
            setSelectedContainer(e as AdserverContainerModel);
          }}
        >
          <SelectTrigger className="w-[300px]">
            <SelectValue placeholder="Brand" />
          </SelectTrigger>
          <SelectContent>
            {Object.keys(ADSERVER_CONTAINER_MAPPER).map((item, index) => {
              return (
                <SelectItem key={index} value={item}>
                  {item}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>
      <div className="grid grid-cols-2 mt-4 gap-4">
        {rules.map((rule, index) => (
          <AdRuleCard key={index} rule={rule} />
        ))}
      </div>
    </div>
  );
}
