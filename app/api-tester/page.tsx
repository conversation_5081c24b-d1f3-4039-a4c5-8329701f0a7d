'use client';
import { loadTest } from "@/actions/loadTest";
import PageLayout from "@/components/pageLayout";

export default function Page() {
    return (
        <PageLayout>
            <p className="text-2xl">API tester</p>
            <form
                onSubmit={async e => {
                    e.preventDefault();
                    const f = (e.target as any).elements;

                    const payload = {
                        url: f.url.value,
                        body: f.body.value,
                        amount: f.amount.value,
                        method: f.method.value,
                        duration: f.duration.value,
                        connections: f.connections.value,
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    }

                    console.log(payload)
                    const result = await loadTest(payload);
                    console.log(result)
                }}
                className="flex flex-col gap-4 flex flex-col items-start">
                <input className="p-4 my-2" name="url" placeholder="URL" />
                <input className="p-4 my-2" name="duration" placeholder="duration" />
                <input className="p-4 my-2" name="amount" placeholder="amount" />
                <input className="p-4 my-2" name="method" placeholder="method" />
                <input className="p-4 my-2" name="body" placeholder="body" />
                <input className="p-4 my-2" name="connections" placeholder="connections" />
                <button className="p-4 my-2 border rounded-md bg-accent" type="submit">SUBMIT</button>
            </form>
        </PageLayout>
    )
}