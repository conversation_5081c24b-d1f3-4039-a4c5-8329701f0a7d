'use client';

import { getDeployStatus } from "@/actions/sfDeployAction";
import PageLayout from "@/components/pageLayout";
import DeploymentStatusTable from "@/components/sf/deploymentStatusTable";
import { Skeleton } from "@/components/ui/skeleton";
import { tBrand, tEnv } from "@/models";
import React from "react";

type tDeployStatus = Record<tEnv, string>

const BRANDS: tBrand[] = [
    'bluehost',
    'hostgator',
    'web',
    'networksolutions',
    'register',
    'domain'
];

function reduceArrayToObj(deployData: any): tDeployStatus {
    const obj: tDeployStatus = deployData.reduce((acc: any, { env, data }: any) => {
        (acc as any)[env] = { dataString: data, env };
        return acc;
    }, {} as tDeployStatus);

    return obj;
}

function extractStringToObj(input: any, env: tEnv): any {
    console.log(env)
    const result: Record<string, string | number | string[]> = {};

    input.dataString.split("\n").forEach((line: any) => {
        const [key, ...valueParts] = line.split(":");

        if (valueParts.length > 0) {
            const value = valueParts.join(":").trim();
            const formattedKey = key.toLowerCase().replace(/\s+/g, "_");
            result[formattedKey] = isNaN(Number(value)) ? value : Number(value);
        }
        result.env = env;
        result.brand = input.env
    });
    return result;
}

export default function Storefront() {
    const [rowsQaMain, setRowsQaMain] = React.useState<any[]>([]);
    const [rowsProd, setRowsProd] = React.useState<any[]>([]);

    async function getData() {
        const prodDeployDataList = BRANDS.map(async i => {
            return {
                env: i,
                data: await getDeployStatus(i, 'prod', 'checkout')
            };
        });

        const qaMainDeployDataList = BRANDS.map(async i => {
            return {
                env: i,
                data: await getDeployStatus(i, 'qamain', 'checkout')
            };
        });

        const prodObj: tDeployStatus = reduceArrayToObj(await Promise.all(prodDeployDataList));
        const qaMainObj: tDeployStatus = reduceArrayToObj(await Promise.all(qaMainDeployDataList));

        setRowsQaMain(Object.keys(qaMainObj).map(key => extractStringToObj((qaMainObj as any)[key], 'qamain')));
        setRowsProd(Object.keys(prodObj).map(key => extractStringToObj((prodObj as any)[key], 'prod')));
    }

    React.useEffect(() => {
        console.log(rowsQaMain)
    }, [rowsQaMain, rowsProd]);

    React.useEffect(() => {
        getData();
    }, []);

    return (<PageLayout>
        <div className="w-full flex flex-col gap-4">
            <div className="flex flex-row items-center gap-4">
                <h2 className="text-base-content text-2xl text-[#aaa]">Storefront Deployment Status</h2>
            </div>
            {rowsProd?.length > 0 ? <DeploymentStatusTable rows={rowsProd} title="PROD" /> : <Skeleton />}
            {rowsQaMain?.length > 0 ? <DeploymentStatusTable rows={rowsQaMain} title="QAMain" /> : <Skeleton />}
        </div>
    </PageLayout>)
}
