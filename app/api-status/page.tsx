"use client";
import PageLayout from "@/components/pageLayout";
import { iServiceResponseStatus, ServiceStatus } from "@/models";
import { fetchAllData } from "@/actions/apiStatus";
import { useEffect, useState } from "react";

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button";

export default function Page() {
    const [responseResults, setResponseResults] = useState<
        iServiceResponseStatus[]
    >([]);
    const [loading, setLoading] = useState<boolean>(true);

    useEffect(() => {
        getEndpointsData()
    }, []);

    const getEndpointsData = async () => {
        setResponseResults([]);
        try {
            const data = (await fetchAllData()) as iServiceResponseStatus[];
            console.log(data);
            setResponseResults(data);
        } finally {
            setLoading(false);
        }
    };

    if (loading)
        return (
            <PageLayout>
                <div>Loading...</div>
            </PageLayout>
        );

    return (
        <PageLayout>
            <div className="flex items-center justify-between flex-column flex-wrap md:flex-row space-y-4 md:space-y-0 pb-4">
                <div>
                    <Button
                        variant={'outline'}
                        onClick={getEndpointsData}
                    >
                        Refresh
                    </Button>
                </div>
            </div>
            <Table className="glass-card">
                <TableHeader>
                    <TableRow>
                        <TableHead className="w-[100px]">Endpoint URL</TableHead>
                        <TableHead>Version</TableHead>
                        <TableHead>Status</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {responseResults.map((item) => (
                        <TableRow key={item.URI}>
                            <TableCell scope="row">
                                {item.URI}
                            </TableCell>
                            <TableCell>{item.version}</TableCell>
                            <TableCell className="">
                                {item.status === ServiceStatus.ONLINE ? (
                                    <div className="flex items-center">
                                        <div className="h-2.5 w-2.5 rounded-full bg-green-500 me-2"></div>{" "}
                                        {ServiceStatus.ONLINE}
                                    </div>
                                ) : (
                                    <div className="flex items-center">
                                        <div className="h-2.5 w-2.5 rounded-full bg-red-500 me-2"></div>{" "}
                                        {ServiceStatus.UNRECHEABLE}
                                    </div>
                                )}
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </PageLayout>
    );
}
