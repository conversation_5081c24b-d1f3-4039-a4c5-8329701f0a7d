'use client';
import React from "react";
import PageLayout from "@/components/pageLayout";
import { domainSearch } from "@/actions/domainSearchAction";

export default function KB() {

    const [searchResults, setSearchResults] = React.useState<any>();

    return (<PageLayout>
        <h1 className="prose prose-xl px-4"> Domain Search</h1>
        <form onSubmit={async e => {
            e.preventDefault();
            const f = (e.target as any).elements;

            const searchDomain: string = f.searchDomain.value;

            const x = await domainSearch(searchDomain);

            console.log(x);
            setSearchResults(x);


        }}>
            <div className="flex flex-row gap-4">
                <input className="w-full h-10 p-2 px-10" name="searchDomain" placeholder="enter your domain..." />
                <button className="bg-accent p-2">search</button>
            </div>

        </form>

        {searchResults && (<div className="flex flex-col gap-4">
            <h2 className="text-2xl">Searched Domain</h2>
            <p className="border-white border p-2">{searchResults.searchedDomains[0].domainName} - {searchResults.searchedDomains[0].unitPriceWithCurrency}</p>
            <h2 className="text-2xl">Spin Domains</h2>
            {searchResults.spinDomains.map((i: any, index: number) => (<p className="border-white border p-2" key={index}>{i.domainName}</p>))}
            <h2>Top TLD Domain</h2>
            {searchResults.topTldDomains.map((i: any, index: number) => (<p className="border-white border p-2" key={index}>{i.domainName}</p>))}
        </div>)}
    </PageLayout>)
}