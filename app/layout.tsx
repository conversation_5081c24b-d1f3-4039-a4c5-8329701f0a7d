'use client';

import localFont from "next/font/local";
import "./globals.css";
import Header from "@/components/header";
import { useState, useEffect } from "react";
import { SessionProvider } from "next-auth/react";
import { ThemeProvider } from "@/components/theme-provider";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/appSidebar";
import { Toaster } from "@/components/ui/sonner";

import bgLight from '@/assets/pictures/pic1.jpg';
import bgDark from '@/assets/pictures/pic7.jpg';

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

const DARKNESS = 0.3;

// Separate client component for dynamic background
function BackgroundManager() {
  const [theme, setTheme] = useState<string>('light');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // Check for theme in localStorage or default to light
    const savedTheme = localStorage.getItem('theme') || 'light';
    
    // Check if HTML has dark class (from ThemeProvider)
    const isDarkMode = document.documentElement.classList.contains('dark');
    
    // Set theme based on class or localStorage
    setTheme(isDarkMode ? 'dark' : savedTheme);
    setMounted(true);
    
    // Set up observer for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          const isDark = document.documentElement.classList.contains('dark');
          setTheme(isDark ? 'dark' : 'light');
          localStorage.setItem('theme', isDark ? 'dark' : 'light');
        }
      });
    });
    
    observer.observe(document.documentElement, { attributes: true });
    
    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!mounted) return;
  
    const backgroundUrl = theme === 'dark' ? bgDark.src : bgLight.src;
    document.body.style.background = `
      linear-gradient(rgba(0, 0, 0, ${DARKNESS}), rgba(0, 0, 0, ${DARKNESS})),
      url(${backgroundUrl}) no-repeat center top
    `;
    document.body.style.backgroundSize = '100% auto';
    document.body.style.backgroundRepeat = 'no-repeat';
    document.body.style.backgroundAttachment = 'fixed';
  }, [theme, mounted]);

  return null; // This component doesn't render anything
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className="h-full">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {/* BackgroundManager handles dynamic background changes */}
          <BackgroundManager />
          
          <SidebarProvider>
            <AppSidebar />
            <main className="w-full">
              <SessionProvider>
                <Header />
                {children}
                <Toaster />
              </SessionProvider>
            </main>
          </SidebarProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}