'use client';
import { clearCache } from "@/actions/cfAction";
import PageLayout from "@/components/pageLayout";
import { Button } from "@/components/ui/button";
import { toast } from "sonner"

const STUBS = [
    "sfbff.newfold.com/getProductPrice",
    "sfbff.newfold.com/getContent",
    "mta.newfold.com/snippets",
    "newfold.com/getContent/json",
    "newfold.com/adServer/getAd",
    "sfbff.newfold.com/static",
];

export default function Page() {
    return (
        <PageLayout>
            <div className="">
                <h2 className="text-lg py-2">BFF Prod</h2>
                <div className="justify-end card-actions grid grid-cols-3 gap-2 glass-card p-4">
                    {STUBS.map((i, index) => (<Button
                        variant={'outline'}
                        className="btn h-16 bg-transparent border-white"
                        key={index}
                        onClick={async () => {
                            const status = await clearCache(`https://${i}`);

                            if (status) {
                                toast.success(`Cache purge initiated for ${i} with ${status}`);
                            }
                            else {
                                toast.error(`Unable to clear cache for ${i}`);
                            }
                            // Swal.fire({
                            //     title: status ? "Good job!" : "Oh no!",
                            //     text: status ? `Cache purge initiated for ${i}. ${status}` : "Something went wrong",
                            //     icon: status ? "success" : "error"
                            // });
                        }}>{i}</Button>))}
                </div>
            </div>
        </PageLayout>
    )
}