'use server';
import axios from "axios";

export async function clearCache(stub: string): Promise<string> {
    const data = JSON.stringify({
        files: [
            stub
        ]
    });

    let returnValue: string = '';

    const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api.cloudflare.com/client/v4/zones/e55013d0654aa908a476b862e066ac9f/purge_cache',
        headers: {
            'Authorization': `Bearer ${process.env.CF_NEWFOLD_TOKEN}`,
            'Content-Type': 'application/json'
        },
        data: data
    };

    try {
        const response = await axios.request(config);
        console.log(JSON.stringify(response.data, null, 4));
        console.log(JSON.stringify(response.status, null, 4));

        if (response.status === 200) {
            returnValue = response.data.result.id;
        }
    }
    catch (e: any) {
        console.error(e.message);
    }

    return returnValue;
}