'use server';

import axios from 'axios';

export async function domainSearch(domain: string) {
    let data = JSON.stringify({
        "request": {
            "fromEdsPath": true,
            "domainNames": [
                domain
            ],
            "useConfigTlds": true,
            "checkExactMatchAvailability": true,
            "spinSearch": true,
            "flowId": "",
            "requestInfo": {
                "service": "DomainAPI",
                "method": "searchDomain",
                "clientId": "NSI",
                "apiAccessKey": "yneujmorzfuezfwrczobxjr5jdg",
                "isloading": true
            }
        }
    });

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://www.web.com/sfcore.do?searchDomain',
        headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/json',
        },
        data: data
    };

    let response;
    try {
        response = await axios.request(config);

        return response.data.response.data;
    }
    catch (e) {
        console.error('error fetching domain');
        return;
    }

}
