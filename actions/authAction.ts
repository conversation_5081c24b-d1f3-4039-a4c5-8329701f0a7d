'use server'

import { createUserIfNotExist } from "@/util/db/userRepository"
import { SESSION_KEY } from "@/constants";
import { UserModel } from "@/util/db/schemas";
import { checkIfUserExists } from "@/util/db/userRepository";
import { buildResponse } from "@/util/response";
import { createSession, deleteAllSessionsForUser, validateSessionToken } from "@/util/session";
import { cookies } from "next/headers";

export async function signupAction(email: string, password: string) {
    await createUserIfNotExist({
        email, password,
        roles: ["default-user"]
    });
}

export async function isUserLoggedIn() {
    const cookieStore = cookies();

    const sessionToken = cookieStore.get(SESSION_KEY)?.value;

    if (sessionToken) {
        const validation = await validateSessionToken(sessionToken);

        if (validation.session && validation.user) {
            // user already logged in
            return buildResponse<UserModel>(validation.user);
        }
    }
    return buildResponse<undefined>();
}

export async function login(email: string, password: string) {
    const cookieStore = cookies();

    const user = await checkIfUserExists(email, password);

    if (user) {
        await deleteAllSessionsForUser(user);
        const generatedSession = await createSession(JSON.stringify(user), user.id!);

        cookieStore.set(SESSION_KEY, generatedSession.id, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: "strict",
            expires: generatedSession.expiresAt
        });

        return buildResponse<UserModel>(user);
    }
    else {
        return buildResponse(undefined, "Invalid credentials");
    }

}