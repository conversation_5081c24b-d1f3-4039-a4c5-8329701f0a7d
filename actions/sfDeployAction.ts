'use server';

import axios from "axios";
import { tEnv, tProduct } from "@/models";


export async function getDeployStatus(brand: string, env: tEnv, stub: tProduct) {
    try {
        console.log(`https://new-proxy-davids-tools.apps.atlanta1.newfoldmb.com/upp/build-info?env=${env}&brand=www.${brand}&path=${stub}`);

        const resp = await axios.get(`https://new-proxy-davids-tools.apps.atlanta1.newfoldmb.com/upp/build-info?env=${env}&brand=www.${brand}&path=${stub}`);

        return resp.data;
    }
    catch (e: any) {
        console.error(e.message);
    }
}
