"use server";

import { STATUS_ENDPOINTS } from "@/constants";
import { iServiceResponseStatus, ServiceStatus } from "@/models";
import axios from "axios";
import { revalidatePath } from "next/cache";

export async function fetchData(uri: string) {
    try {
        const response = await axios.get(uri);
        const serviceStatus: iServiceResponseStatus = {
            URI: uri,
            version: response.data?.version,
            status: ServiceStatus.ONLINE,
        };
        return serviceStatus;
    } catch (error) {
        console.log(`Error fetching data from ${uri}:`, error);
        const errorResult: iServiceResponseStatus = {
            URI: uri,
            version: "0",
            status: ServiceStatus.UNRECHEABLE,
        };
        return errorResult;
    }
}

export async function fetchAllData() {
    try {
        let responseResults: iServiceResponseStatus[] = [];
        const promises = STATUS_ENDPOINTS.map(fetchData);
        await Promise.allSettled(promises).then((results) => {
            results
                .filter((result) => result.status === "fulfilled")
                .map((result) => responseResults.push(result.value));
            results
                .filter((result) => result.status === "rejected")
                .map((result) => responseResults.push(result.reason));
        });
        revalidatePath("/api-status");
        return responseResults;
    } catch (error) {
        console.error("Error fetching all data:", error);
    }
}
