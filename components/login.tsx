'use client';
import React from "react";

import { useSession, signIn, signOut } from 'next-auth/react';
import { useUserStore } from "@/stores/userStore";
import { MsOauthResponseModel, tRole } from "@/models";
import { AUTH_ROLES } from "@/constants";
import { davidsDashboardRoleToTRole } from "@/util/auth";

export default function Login() {
    const { data: session } = useSession();
    const { setUser } = useUserStore();

    React.useEffect(() => {
        if (session) {

            // remove the admin role, testing purposes

            // (session as unknown as MsOauthResponseModel).roles.forEach((i, index) => {
            //     if (i === "251cc2f8-1746-411d-8603-d587ab946bac") delete (session as unknown as MsOauthResponseModel).roles[index];
            // });

            const roles: tRole[] = [];

            // converting that weird alphanumeric string to tRole
            (session as unknown as <PERSON><PERSON>authResponseModel).roles.map(role => {
                if (role in AUTH_ROLES) {
                    roles.push(davidsDashboardRoleToTRole(AUTH_ROLES[role]) as tRole);
                }
            }).filter(Boolean);

            (session as unknown as MsOauthResponseModel).roles = roles;
            setUser(session as unknown as MsOauthResponseModel);
        }
    }, [session, setUser]);

    if (session) {
        return (
            <div>
                <p>Welcome, {session?.user?.name}</p>
                <button className="btn" onClick={() => signOut()}>Sign out</button>
            </div>
        );
    }
    return <button onClick={() => signIn("microsoft")}>Sign in with Microsoft</button>;
}