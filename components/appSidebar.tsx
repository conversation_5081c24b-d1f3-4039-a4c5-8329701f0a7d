import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { useUserStore } from "@/stores/userStore"
import { userHasRole } from "@/util/auth";
import { DRAWER_ITEMS, OPEN_DRAWER_ITEMS } from "@/constants";
import Link from "next/link";
import { signIn } from "next-auth/react";

function AppSideBarContent() {
  const { user } = useUserStore();

  if (user && userHasRole(user, 'admin')) {
    return Object.keys(DRAWER_ITEMS).map((item, index) => (
      <div key={index} className="py-1">
        <SidebarMenuItem key={item}>
          <Link href={`/${item}`}>
            <SidebarMenuButton className="flex flex-row gap-4">
              {(DRAWER_ITEMS as any)[item]}
              {item}
            </SidebarMenuButton>
          </Link>
        </SidebarMenuItem>
      </div>
    ))
  }
  else return (user ?
    user.roles.map((item) => {
      return (
        // <li key={index}><Link href={`/${item}`}>{item}</Link></li>
        <SidebarMenuItem key={item}>
          <Link href={`/${item}`}>
            <SidebarMenuButton className="flex flex-row gap-4">
              {(DRAWER_ITEMS as any)[item]}
              {item}
            </SidebarMenuButton>
          </Link>
        </SidebarMenuItem>
      )
    })
    : <button onClick={() => signIn("microsoft")} className="btn">pls login</button>)
}

export function AppSidebar() {

  // if (user && userHasRole(user, 'admin')) {
  //   return (<div className="z-50 border-r-[1px] border-[#777]">
  //     <ul className="menu text-base-content min-h-svh w-80 p-4">
  // {Object.keys(DRAWER_ITEMS).map((item, index) => (
  //   <li key={index} className="py-1">
  //     <Link href={`/${item}`}>
  //       <div className="flex flex-row items-center">
  //         {(DRAWER_ITEMS as any)[item]}
  //         {item}
  //       </div>
  //     </Link>
  //   </li>
  // )
  // )}
  //     </ul>
  //   </div>)
  // }

  return (
    <Sidebar collapsible="icon">
      <SidebarContent style={{
        background: "rgba(255, 255, 255, 0.25)",
        // boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
        backdropFilter: "blur(8px)",
        WebkitBackdropFilter: "blur(8px)",
        // borderRadius: "10px",
        // border: "1px solid rgba(255, 255, 255, 0.18)"
      }}>
        <SidebarGroup>
          <SidebarGroupLabel>
            <Link href={'/'}>Davids&apos;s Dashboard</Link>
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <AppSideBarContent />
              {Object.keys(OPEN_DRAWER_ITEMS).map((item) => {
                return (<SidebarMenuItem key={item}>
                  <Link href={item}>
                    <SidebarMenuButton className="flex flex-row gap-4">
                      {OPEN_DRAWER_ITEMS[item]}
                      {item}
                    </SidebarMenuButton>
                  </Link>
                </SidebarMenuItem>)
              })}
              {/* <SidebarMenuItem>
                <Link href='/adserver'>
                  <SidebarMenuButton>
                    {OPEN_DRAWER_ITEMS['adserver-renderapp']}
                  </SidebarMenuButton>
                </Link>
              </SidebarMenuItem> */}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );

  // else
  //   return (<div className="z-50 border-r-[1px] border-[#777] bg-accent">
  //     <ul className="menu text-base-content min-h-svh w-80 p-4">
  //       {user ?
  //         user.roles.map((item, index) => {

  //           return (
  //             <li key={index}><Link href={`/${item}`}>{item}</Link></li>
  //           )
  //         })
  //         : <button onClick={() => signIn("microsoft")} className="btn">pls login</button>}
  //     </ul>
  //   </div>)
  // return (
  //   <Sidebar>
  //     <SidebarContent>
  //       <SidebarGroup>
  //         <SidebarGroupLabel>Application</SidebarGroupLabel>
  //         <SidebarGroupContent>
  //           <SidebarMenu>
  //             {items.map((item) => (
  //               <SidebarMenuItem key={item.title}>
  //                 <SidebarMenuButton asChild>
  //                   <a href={item.url}>
  //                     <item.icon />
  //                     <span>{item.title}</span>
  //                   </a>
  //                 </SidebarMenuButton>
  //               </SidebarMenuItem>
  //             ))}
  //           </SidebarMenu>
  //         </SidebarGroupContent>
  //       </SidebarGroup>
  //     </SidebarContent>
  //   </Sidebar>
  // )
}
