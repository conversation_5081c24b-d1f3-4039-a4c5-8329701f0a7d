'use client';

import { KB_BRANDS, KB_CLEAR_CACHE_TOOLTIP } from "@/constants";
import React from "react";

export default function Cache() {

    const [, setBrand] = React.useState<string>('');
    return (<>
        <div className="card bg-base-100 shadow-xl border border-2-gray">
            <div className="card-body">
                <div className="flex flex-row w-full items-center justify-start gap-4 px-4">
                    <p className="">Clear the cache of</p>
                    <select
                        className="select select-bordered w-full max-w-xs"
                        defaultValue={'brand'}
                        onChange={e => {
                            setBrand(e.target.value)
                        }}
                    >
                        <option disabled>choose a brand...</option>
                        {KB_BRANDS.map((i, index) => (<option key={index}>{i}</option>))}
                    </select>
                    <button className="btn btn-accent">Clear</button>
                    <div className="lg:tooltip" data-tip={KB_CLEAR_CACHE_TOOLTIP}>
                        <button className="btn">What does this mean?</button>
                    </div>
                </div>
            </div>
        </div>
    </>)
}