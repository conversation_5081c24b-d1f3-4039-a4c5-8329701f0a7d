import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Copy, Link2 } from "lucide-react";
import { Textarea } from "./ui/textarea";
import JsonPreviewBox from "./jsonPreviewBox";

const paramList = [
    "sku",
    "brand",
    "bs",
    "couponCode",
    "currencyCode",
    "baseCurrencyCode",
    "locale",
    "env",
    "siteId",
    "transform",
    "transactionType",
    "productLevelChannelId",
    "excludeFOS",
    "userRef",
];

export default function BffUrlBuilder() {
    const [params, setParams] = useState({
        env: "prod", brand: 'WEB', bs: 'JARVIS',
        currencyCode: "USD", sku: 'WEBSITE'
    });

    const handleChange = (key: string, value: string) => {
        setParams((prev) => ({ ...prev, [key]: value }));
    };

    const queryString = Object.entries(params)
        .filter(([key, value]) => key !== "sku" && value !== undefined && value !== "")
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join("&");

    const fullUrl = `https://sfbff.newfold.com/getProductPrice/product/${params.sku || "WEBSITE"}?${queryString}`;

    const [debouncedUrl, setDebouncedUrl] = useState(fullUrl);

    const handleCopy = () => {
        navigator.clipboard.writeText(fullUrl);
    };

    useEffect(() => {
        const timeout = setTimeout(() => {
            setDebouncedUrl(fullUrl);
        }, 2000); // 2s debounce

        return () => clearTimeout(timeout); // clear previous timeout
    }, [fullUrl]);

    return (<div className="grid grid-cols-2">
        <div className="w-full px-10 mx-auto p-4 space-y-4">
            <h1 className="text-2xl font-bold">URL Builder</h1>
            <Card className="bg-transparent glass-card">
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                    {paramList.map((param) => (
                        <div key={param}>
                            <Label htmlFor={param}>{param}</Label>
                            <Input
                                className="border-transparent"
                                id={param}
                                placeholder={`Enter ${param}`}
                                value={(params as any)[param] || ""}
                                onChange={(e) => handleChange(param, e.target.value)}
                            />
                        </div>
                    ))}
                </CardContent>
            </Card>
            <div className="space-y-2">
                <Label>Generated URL</Label>
                <div className="flex items-center gap-2 glass-card px-2">
                    <Textarea readOnly value={fullUrl} className="flex-1 h-16 border-transparent" />
                    <Button onClick={handleCopy} variant="secondary" size="icon">
                        <Copy className="w-4 h-4" />
                    </Button>
                    <a href={fullUrl} target="_blank" rel="noopener noreferrer">
                        <Button variant="outline" size="icon">
                            <Link2 className="w-4 h-4" />
                        </Button>
                    </a>
                </div>
            </div>
        </div>
        <div className="py-16">
            <JsonPreviewBox url={debouncedUrl} />
        </div>
    </div>);
}
