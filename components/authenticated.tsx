'use server';

import { SESSION_KEY } from "@/constants";
import { cookies } from "next/headers";

function LoadingSkeleton() {
    return (<div className="flex w-52 flex-col gap-4">
        <div className="skeleton h-32 w-full"></div>
        <div className="skeleton h-4 w-28"></div>
        <div className="skeleton h-4 w-full"></div>
        <div className="skeleton h-4 w-full"></div>
    </div>)
}

function NotAuthenticated() {
    return (<div>Not Authenticated</div>)
}

export default async function Authenticated({ children }: Readonly<{
    children: React.ReactNode;
}>) {
    const sessionCookie = cookies().get(SESSION_KEY)?.value;

    if (!sessionCookie) {
        return <LoadingSkeleton />
    }

    const isValid = false;

    if (!isValid) {
        return <NotAuthenticated />
    }

    return <>{children}</>
}