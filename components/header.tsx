'use client';

import React from "react";
import { useUserStore } from "@/stores/userStore";
import Link from "next/link";
import { signIn, signOut, useSession } from "next-auth/react";
import { CircleUser } from 'lucide-react';

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "./ui/button";
import { ThemeToggle } from "./themeToggle";
import { SidebarTrigger } from "./ui/sidebar";
import { MsOauthResponseModel, tRole } from "@/models";
import { AUTH_ROLES } from "@/constants";
import { davidsDashboardRoleToTRole } from "@/util/auth";


export default function Header() {
    const { user, setUser } = useUserStore();
    const { data } = useSession();

    React.useEffect(() => {
        if (data) {

            // remove the admin role, testing purposes

            // (data as unknown as MsOauthResponseModel).roles.forEach((i, index) => {
            //     if (i === "251cc2f8-1746-411d-8603-d587ab946bac") delete (data as unknown as MsOauthResponseModel).roles[index];
            // });

            const roles: tRole[] = [];

            // converting that weird alphanumeric string to tRole
            (data as unknown as MsOauthResponseModel).roles.map(role => {
                if (role in AUTH_ROLES) {
                    roles.push(davidsDashboardRoleToTRole(AUTH_ROLES[role]) as tRole);
                }
            }).filter(Boolean);

            (data as unknown as MsOauthResponseModel).roles = roles;
            setUser(data as unknown as MsOauthResponseModel);
        }
    }, [data, setUser]);

    return (<nav className="flex flex-row w-full items-center justify-between">
        <SidebarTrigger className="m-1" />
        <Link href="/" className="btn btn-ghost text-xl">David&apos;s Dashboard</Link>


        {user ? <>
            <div className="flex flex-row items-center gap-2">
                <ThemeToggle />
                <DropdownMenu>
                    <DropdownMenuTrigger>
                        <Button asChild variant={'ghost'} size={'icon'} className="m-1 p-2">
                            <CircleUser />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>

                        <div>
                            <DropdownMenuLabel>Welcome {user.name}</DropdownMenuLabel>

                            <DropdownMenuItem>
                                Settings
                            </DropdownMenuItem>

                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => signOut()}>Logout</DropdownMenuItem>
                        </div>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div></> : <div className="flex flex-row items-center">
            <ThemeToggle />
            <Button variant={'outline'} onClick={() => signIn("microsoft")} className="m-2">Login</Button></div>}
    </nav>);
}
