'use client';

import React from "react";

import { tBrand, tEnv, tProduct } from "@/models";
import { getDeployStatus } from "@/actions/sfDeployAction";

const BRANDS: tBrand[] = [
    'bluehost',
    'hostgator',
    'web',
    'networksolutions',
    'register',
    'domain'
];

interface Props {
    env: tEnv;
    stub: tProduct
}

type tDeployStatus = Record<tEnv, string>

export default function DeploymentStatus({ env }: Props) {
    const [deployment, setDeployment] = React.useState<tDeployStatus>();

    const getData = React.useCallback(async () => {
        const deployData = await Promise.all(
            BRANDS.map(async i => {
                return {
                    env: i,
                    data: await getDeployStatus(i, env, 'checkout')
                };
            })
        );

        const obj: Record<tEnv, any> = deployData.reduce((acc, { env, data }) => {
            (acc as any)[env] = data;
            return acc;
        }, {} as Record<tEnv, any>);
        setDeployment(obj);

    }, [env, setDeployment]);

    function formatDeploymentInfo(deployment: tDeployStatus, i: tBrand) {
        return (deployment as any)[i].split('\n').map((y: string, index2: number)=> (<div key={index2}>{y}</div>))
    }

    React.useEffect(() => {
        getData();
    }, [env, getData]);

    return (<div className="">
        <p className="text-xl">{env.toUpperCase()}</p>
        <div className="pl-8 grid grid-cols-3 gap-4">
            {BRANDS.map((i, index) => (<div className="p-2" key={index}>
                <div className="text-lg font-bold mb-2 text-neutral-content">{i}</div>
                {deployment && (deployment as any)[i] ? (<div className="text-[#aaa] w-full h-40 p-2 rounded-xl bg-secondary border">{formatDeploymentInfo(deployment, i)}</div>) : (<div className="skeleton h-32 w-full"></div>)}
            </div>))}
        </div>
    </div>);
}
