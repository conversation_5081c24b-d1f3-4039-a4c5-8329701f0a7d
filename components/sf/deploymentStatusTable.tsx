'use client';

import { RowData } from "@/models"
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Card } from "../ui/card";

interface Props {
    rows: RowData[];
    title: string;
}

export default function DeploymentStatusTable({ rows, title }: Props) {
    return (<div className="overflow-x-auto rounded-lg p-2">
        <Card className="bg-transparent glass-card-1" >
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead className="w-[100px]">{title}</TableHead>
                        <TableHead>Brand</TableHead>
                        <TableHead>Branch</TableHead>
                        <TableHead>Build</TableHead>
                        <TableHead>Build File</TableHead>
                        <TableHead>Install Date</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {rows.map((i, index) => (<TableRow key={`${title}-${i.brand}-${index}`}>
                        <TableCell></TableCell>
                        <TableCell>{i.brand}</TableCell>
                        <TableCell>{i.branch}</TableCell>
                        <TableCell>{i.build}</TableCell>
                        <TableCell>{i.build_file}</TableCell>
                        <TableCell>{i.install_date}</TableCell>
                    </TableRow>))}
                </TableBody>
            </Table>
        </Card>

    </div>)
}
