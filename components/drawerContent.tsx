'use client';

import React from "react";
import {
    Sidebar,
    SidebarContent,
} from "@/components/ui/sidebar"

export default function AppDrawer() {

    return (<>
        <Sidebar>
            <SidebarContent />
        </Sidebar>

    </>)

    // if (user && userHasRole(user, 'admin')) {
    //     return (<div className="z-50 border-r-[1px] border-[#777]">
    //         <ul className="menu text-base-content min-h-svh w-80 p-4">
    //             {Object.keys(DRAWER_ITEMS).map((item, index) => (
    //                 <li key={index} className="py-1">
    //                     <Link href={`/${item}`}>
    //                         <div className="flex flex-row items-center">
    //                             {(DRAWER_ITEMS as any)[item]}
    //                             {item}
    //                         </div>
    //                     </Link>
    //                 </li>
    //             )
    //             )}
    //         </ul>
    //     </div>)
    // }
    // return (<div className="z-50 border-r-[1px] border-[#777] bg-accent">
    //     <ul className="menu text-base-content min-h-svh w-80 p-4">
    //         {user ?
    //             user.roles.map((item, index) => {

    //                 return (
    //                     <li key={index}><Link href={`/${item}`}>{item}</Link></li>
    //                 )
    //             })
    //             : <button onClick={() => signIn("microsoft")} className="btn">pls login</button>}
    //     </ul>
    // </div>)

}
