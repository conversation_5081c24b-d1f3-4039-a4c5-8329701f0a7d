'use client';
import React from "react";
// import { signupAction } from "@/actions/authAction";

// import Swal from "sweetalert2";

export default function SignUp() {
    return (
        <div className="p-2">
            <form
                onSubmit={async (e) => {
                    e.preventDefault();
                    console.log((e.target as any).elements.email.value)
                    console.log((e.target as any).elements.password.value)

                    // await signupAction(
                    //     (e.target as any).elements.email.value,
                    //     (e.target as any).elements.password.value,
                    // );

                    // Swal.fire({
                    //     title: 'User created',
                    //     text: 'User created if it doesnt exist already',
                    //     icon: 'success',
                    //     showConfirmButton: false,
                    //     timer: 1500
                    //   })
                }}
                className="flex flex-col gap-2">
                <label className="input input-bordered flex items-center gap-2">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 16 16"
                        fill="currentColor"
                        className="h-4 w-4 opacity-70">
                        <path
                            d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6ZM12.735 14c.618 0 1.093-.561.872-1.139a6.002 6.002 0 0 0-11.215 0c-.22.578.254 1.139.872 1.139h9.47Z" />
                    </svg>
                    <input type="text" className="grow" placeholder="Username" name="email" />
                </label>
                <label className="input input-bordered flex items-center gap-2">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 16 16"
                        fill="currentColor"
                        className="h-4 w-4 opacity-70">
                        <path
                            fillRule="evenodd"
                            d="M14 6a4 4 0 0 1-4.899 3.899l-1.955 1.955a.5.5 0 0 1-.353.146H5v1.5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5v-2.293a.5.5 0 0 1 .146-.353l3.955-3.955A4 4 0 1 1 14 6Zm-4-2a.75.75 0 0 0 0 ******* 0 0 1 .********* 0 0 0 1.5 0 2 2 0 0 0-2-2Z"
                            clipRule="evenodd" />
                    </svg>
                    <input type="password" className="grow" name="password" />
                </label>

                <button className="p-2 w-full border border-2-gray rounded-md" type="submit">SIGN UP</button>
            </form>
        </div>
    )
}