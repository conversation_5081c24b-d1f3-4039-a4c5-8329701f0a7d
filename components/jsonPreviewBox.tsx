import useSWR from "swr"
import { JsonEditor, githubDarkTheme } from 'json-edit-react';

type CacheModel = 'HIT' | 'MISS' | 'DYNAMIC';

export interface Props {
    url: string;
    response: any;
    cached: CacheModel;
    name: string;
};


const fetcher = (...args: any) => fetch(args).then(res => res.json())

function CacheStatus({ status }: { status: CacheModel }) {

    function getColor() {
        switch (status) {
            case 'DYNAMIC':
                return '#FFBF00';
            case 'HIT':
                return '#548234';
            case 'MISS':
                return '#900c3f';
        }
    }

    return (<div style={{
        backgroundColor: getColor()
    }}
        className="p-2 px-6 rounded-xl text-white">{status}</div>);
}


function NetworkResponse(props: Props) {
    return (<div className="w-full flex flex-col items-center border border-2-gray rounded-xl pb-4">
        <div className="flex flex-row items-center justify-center gap-4">
            <p className="text-xl py-4 w-full text-center">{props.name}</p>
            <CacheStatus status={props.cached} />
        </div>
        <JsonEditor
            className="w-full"
            data={props.response}
            theme={githubDarkTheme}
            searchFilter={"all"}
            collapse={1}
            enableClipboard
        />
    </div>)
}

export default function JsonPreviewBox({ url }: { url: string }) {
    const { data, isLoading } = useSWR(url, fetcher)

    if (isLoading) return (<span className="loading loading-spinner loading-xl"></span>)
    return (<div>
        <NetworkResponse cached="HIT"
            response={data}
            url={url}
            name="DS prices" />
    </div>)
}