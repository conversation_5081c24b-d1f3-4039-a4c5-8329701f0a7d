{"name": "davids-tools", "version": "0.1.0", "private": true, "org": "davids-tools", "scripts": {"dev": "PORT=3001 next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.0", "@radix-ui/react-visually-hidden": "^1.1.3", "autocannon": "^8.0.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dotenv": "^16.4.7", "drizzle-orm": "^0.38.0", "https-proxy-agent": "^7.0.6", "json-edit-react": "^1.17.2", "jsonwebtoken": "^9.0.2", "lucide": "^0.487.0", "lucide-react": "^0.487.0", "next": "14.2.20", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "pg": "^8.13.1", "react": "^18", "react-countdown": "^2.3.6", "react-dom": "^18", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zustand": "^5.0.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/autocannon": "^7.12.5", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/pg": "^8.11.10", "@types/react": "^18", "@types/react-dom": "^18", "daisyui": "^4.12.14", "drizzle-kit": "^0.30.0", "eslint": "^8", "eslint-config-next": "14.2.20", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.2", "typescript": "^5"}, "engines": {"node": "^20"}}