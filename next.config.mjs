/** @type {import('next').NextConfig} */
const nextConfig = {
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'img.daisyui.com',
            },
        ],
    },
    experimental: {
        serverActions: {
            allowedOrigins: [
                'dashboard-davids-tools.apps.atlanta1.newfoldmb.com',
                'davidsdashboard-newfolddigital.msappproxy.net'
            ]
        }
    }
};

export default nextConfig;
